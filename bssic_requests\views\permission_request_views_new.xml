<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Permission Request Form View -->
    <record id="view_bssic_permission_request_form_new" model="ir.ui.view">
        <field name="name">bssic.request.permission.form.new</field>
        <field name="model">bssic.request</field>
        <field name="inherit_id" ref="bssic_requests.view_bssic_request_form"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <!-- Override the header to customize buttons for permission requests -->
            <xpath expr="//header" position="replace">
                <header>
                    <button name="action_submit" string="Submit" type="object" class="oe_highlight" states="draft"/>
                    <button name="action_approve_direct_manager" string="Approve (Direct Manager)" type="object" class="oe_highlight"
                            states="direct_manager" groups="bssic_requests.group_bssic_direct_manager"/>
                    <button name="action_approve_audit_manager" string="Approve (Audit Manager)" type="object" class="oe_highlight"
                            states="audit_manager" groups="bssic_requests.group_bssic_audit_manager"/>
                    <button name="action_approve_it_manager" string="Approve (IT Manager)" type="object" class="oe_highlight"
                            states="it_manager" groups="bssic_requests.group_bssic_it_manager"/>
                    <button name="action_assign" string="Assign to IT Staff" type="object" class="oe_highlight"
                            states="assigned" groups="bssic_requests.group_bssic_it_manager"/>
                    <button name="action_complete" string="Mark as Completed" type="object" class="oe_highlight"
                            states="in_progress" groups="bssic_requests.group_bssic_it_staff"/>
                    <button name="action_reject" string="Reject" type="object" class="btn-danger"
                            states="direct_manager,audit_manager,it_manager,assigned,in_progress"/>
                    <field name="is_technical" invisible="1"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,submitted,direct_manager,audit_manager,it_manager,assigned,in_progress,completed"/>
                </header>
            </xpath>
        </field>
    </record>

    <!-- Note: action_bssic_permission_request is defined in request_views.xml to avoid duplication -->
</odoo>
