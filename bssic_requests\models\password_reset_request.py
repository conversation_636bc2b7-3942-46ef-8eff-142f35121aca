from odoo import models, fields, api, _
from odoo.exceptions import UserError


class BSSICPasswordResetRequest(models.Model):
    """Password Reset Request Model"""
    _name = 'bssic.password.reset.request'
    _description = 'BSSIC Password Reset Request'
    _inherit = 'bssic.base.request'

    # Password reset specific fields
    username = fields.Char('Username', tracking=True)
    device_type = fields.Selection([
        ('internet', 'Internet'),
        ('system', 'System'),
        ('swift', 'Swift'),
        ('other', 'Other')
    ], string='Device Type', tracking=True)
    request_reason = fields.Selection([
        ('password_reset', 'Password Reset (Forgotten/Unable to login)'),
        ('account_reactivation', 'Account Reactivation (Account/Device locked)')
    ], string='Request Reason', tracking=True)

    @api.constrains('username', 'device_type', 'request_reason', 'state', 'show_password_fields')
    def _check_required_password_fields(self):
        """Validate required fields for password reset requests"""
        for record in self:
            if record.show_password_fields and record.state != 'draft':
                if not record.username:
                    raise UserError(_('Username is required for Password Reset requests.'))
                if not record.device_type:
                    raise UserError(_('Device Type is required for Password Reset requests.'))
                if not record.request_reason:
                    raise UserError(_('Request Reason is required for Password Reset requests.'))

    @api.model
    def create(self, vals):
        """Override create to set password reset specific defaults"""
        # Set request type code for password reset
        if not vals.get('request_type_code'):
            vals['request_type_code'] = 'password_reset'
        
        # Find and set the password reset request type
        if not vals.get('request_type_id'):
            request_type = self.env['bssic.request.type'].search([
                ('code', '=', 'password_reset')
            ], limit=1)
            if request_type:
                vals['request_type_id'] = request_type.id

        return super(BSSICPasswordResetRequest, self).create(vals)

    @api.model
    def default_get(self, fields_list):
        """Set default values for password reset requests"""
        res = super(BSSICPasswordResetRequest, self).default_get(fields_list)
        
        # Set default request type for password reset
        if 'request_type_code' in fields_list:
            res['request_type_code'] = 'password_reset'
        
        if 'request_type_id' in fields_list:
            request_type = self.env['bssic.request.type'].search([
                ('code', '=', 'password_reset')
            ], limit=1)
            if request_type:
                res['request_type_id'] = request_type.id

        return res
