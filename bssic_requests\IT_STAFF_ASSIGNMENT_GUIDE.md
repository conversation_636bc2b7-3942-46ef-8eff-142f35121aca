# دليل تعيين موظفي تقنية المعلومات في طلبات BSIC

## نظرة عامة / Overview

تم تحسين حقل "Assigned To" في مديول bssic_requests ليظهر فقط الموظفين الذين لديهم صلاحية **IT Staff**.

## التحسينات المطبقة / Applied Improvements

### 1. تحسين Domain للحقل
- **الدالة**: `_get_it_staff_domain()`
- **الوظيفة**: البحث عن جميع المستخدمين الذين لديهم صلاحية `bssic_requests.group_bssic_it_staff`
- **النتيجة**: عرض الموظفين المرتبطين بهؤلاء المستخدمين فقط

### 2. التحقق من الصلاحيات
- **الدالة**: `_validate_it_staff_assignment()`
- **الوظيفة**: التحقق من أن الموظف المختار لديه فعلاً صلاحية IT Staff
- **التطبيق**: في دالة `action_assign()` قبل تعيين الطلب

### 3. Constraint للتحقق
- **الدالة**: `_check_assigned_to_it_staff()`
- **الوظيفة**: منع حفظ الطلب إذا كان الموظف المختار لا يملك صلاحية IT Staff
- **رسالة الخطأ**: واضحة ومفيدة للمستخدم

### 4. تحسينات الواجهة
- **إضافة**: `no_quick_create` لمنع الإنشاء السريع
- **إضافة**: `help` text لتوضيح الغرض من الحقل
- **تحسين**: رسائل الخطأ باللغة العربية والإنجليزية

## كيفية العمل / How It Works

### 1. عند فتح النموذج:
```python
def _get_it_staff_domain(self):
    # البحث عن مجموعة IT Staff
    it_staff_group = self.env.ref('bssic_requests.group_bssic_it_staff')
    
    # البحث عن المستخدمين في هذه المجموعة
    it_staff_users = self.env['res.users'].search([
        ('groups_id', 'in', [it_staff_group.id]),
        ('active', '=', True)
    ])
    
    # البحث عن الموظفين المرتبطين بهؤلاء المستخدمين
    it_staff_employees = self.env['hr.employee'].search([
        ('user_id', 'in', it_staff_users.ids),
        ('active', '=', True)
    ])
    
    return [('id', 'in', it_staff_employees.ids)]
```

### 2. عند اختيار موظف:
```python
@api.constrains('assigned_to')
def _check_assigned_to_it_staff(self):
    for record in self:
        if record.assigned_to and record.assigned_to.user_id:
            if not record._validate_it_staff_assignment():
                raise UserError(_('الموظف المختار لا يملك صلاحية IT Staff'))
```

### 3. عند تعيين الطلب:
```python
def action_assign(self):
    # التحقق من الصلاحيات
    if not self._validate_it_staff_assignment():
        raise UserError(_('يجب اختيار موظف لديه صلاحية IT Staff'))
    
    # متابعة عملية التعيين...
```

## المجموعات المشمولة / Included Groups

### IT Staff Group:
- **المعرف**: `bssic_requests.group_bssic_it_staff`
- **الوصف**: موظفو تقنية المعلومات
- **الصلاحيات**: تنفيذ الطلبات المعينة إليهم

### IT Manager Group:
- **المعرف**: `bssic_requests.group_bssic_it_manager`
- **الوراثة**: يرث من `group_bssic_it_staff`
- **الصلاحيات**: موافقة وتعيين الطلبات + تنفيذها

## رسائل الخطأ / Error Messages

### عند اختيار موظف غير مؤهل:
```
The selected employee "اسم الموظف" does not have IT Staff permissions. 
Please select an employee who is a member of the IT Staff group.
```

### عند محاولة التعيين لموظف غير مؤهل:
```
The selected employee does not have IT Staff permissions. 
Please select a valid IT staff member.
```

## التسجيل والمراقبة / Logging & Monitoring

### معلومات مفيدة في السجلات:
```python
_logger.info(f"Found {len(it_staff_employees)} IT staff employees: {it_staff_employees.mapped('name')}")
```

### تسجيل الأخطاء:
```python
_logger.error(f"Error getting IT staff domain: {str(e)}")
_logger.error(f"Error validating IT staff assignment: {str(e)}")
```

## اختبار الوظيفة / Testing the Feature

### 1. إنشاء مستخدمين للاختبار:
- مستخدم بصلاحية IT Staff ✅
- مستخدم بدون صلاحية IT Staff ❌
- مستخدم بصلاحية IT Manager ✅

### 2. ربط المستخدمين بموظفين:
- إنشاء سجلات موظفين مرتبطة بالمستخدمين

### 3. اختبار السيناريوهات:
- فتح طلب في حالة "Assigned"
- محاولة اختيار موظف بدون صلاحية IT Staff
- التحقق من ظهور رسالة الخطأ
- اختيار موظف بصلاحية IT Staff
- التحقق من نجاح العملية

## الملفات المحدثة / Updated Files

### 1. `models/base_request.py`:
- إضافة `_get_it_staff_domain()`
- إضافة `_validate_it_staff_assignment()`
- إضافة `_check_assigned_to_it_staff()`
- تحسين `action_assign()`

### 2. `views/request_views.xml`:
- تحسين حقل `assigned_to`
- إضافة `no_quick_create`
- إضافة `help` text

## الفوائد / Benefits

### 1. الأمان:
- ضمان تعيين الطلبات للأشخاص المؤهلين فقط
- منع الأخطاء في التعيين

### 2. سهولة الاستخدام:
- عرض الخيارات المناسبة فقط
- رسائل خطأ واضحة ومفيدة

### 3. الموثوقية:
- التحقق على مستويات متعددة
- تسجيل شامل للأخطاء

### 4. الصيانة:
- كود منظم وقابل للصيانة
- دوال مساعدة قابلة لإعادة الاستخدام

## ملاحظات مهمة / Important Notes

### 1. المتطلبات:
- يجب أن يكون للموظف سجل مستخدم مرتبط
- يجب أن يكون المستخدم عضواً في مجموعة IT Staff

### 2. الاستثناءات:
- الموظفون بدون مستخدمين مرتبطين لن يظهروا
- المستخدمون غير النشطين لن يظهروا

### 3. التوافق:
- يعمل مع جميع أنواع الطلبات
- متوافق مع النظام الحالي للصلاحيات

هذا التحسين يضمن أن حقل "Assigned To" يظهر فقط الموظفين المؤهلين لتنفيذ طلبات تقنية المعلومات! 🎯
