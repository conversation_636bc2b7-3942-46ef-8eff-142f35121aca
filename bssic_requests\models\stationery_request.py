from odoo import models, fields, api, _
from odoo.exceptions import UserError


class StationeryRequest(models.Model):
    """Stationery Request Model - now inherits from stationery base"""
    _name = 'bssic.stationery.request'
    _description = 'Stationery Request'
    _inherit = 'bssic.stationery.base'

    @api.model
    def create(self, vals):
        """Override create to set sequence number"""
        if vals.get('name', _('New')) == _('New'):
            vals['name'] = self.env['ir.sequence'].next_by_code('bssic.stationery.request') or _('New')
        return super(StationeryRequest, self).create(vals)

    def action_submit(self):
        """Submit stationery request for approval"""
        # Check if there are any stationery items selected
        if not self.line_ids:
            raise UserError(_('You cannot submit an empty request. Please select at least one stationery item.'))

        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.stationery.request', self.id, 'submitted',
                notes=_('Stationery request submitted for approval'),
                old_state='draft', new_state='direct_manager'
            )

        # Record submission information
        self.submission_user_id = self.env.user.id
        self.submission_date = fields.Datetime.now()

        # First set state to submitted
        self.state = 'submitted'
        # Then move to direct manager approval
        self.state = 'direct_manager'

        # Notify direct manager
        direct_manager = self.employee_id.parent_id
        if direct_manager and direct_manager.user_id:
            self.message_subscribe(partner_ids=[direct_manager.user_id.partner_id.id])
            self.message_post(
                body=_('A new stationery request has been submitted for your approval.'),
                partner_ids=[direct_manager.user_id.partner_id.id]
            )

    def action_approve_direct_manager(self):
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.stationery.request', self.id, 'direct_manager_approved',
                notes=_('Approved by Direct Manager'),
                old_state='direct_manager', new_state='warehouse_approval'
            )

        # Record approval information
        self.direct_manager_approval_user_id = self.env.user.id
        self.direct_manager_approval_date = fields.Datetime.now()

        # Update state to warehouse approval
        self.state = 'warehouse_approval'

        # Find Warehouse manager and notify
        warehouse_group = self.env.ref('bssic_requests.group_bssic_warehouse_manager')
        warehouse_users = warehouse_group.users
        partner_ids = warehouse_users.mapped('partner_id.id')
        if partner_ids:
            self.message_subscribe(partner_ids=partner_ids)
            self.message_post(
                body=_('This stationery request has been approved by the direct manager and requires warehouse verification.'),
                partner_ids=partner_ids
            )

    def action_approve_warehouse(self):
        # Check if all items have stock quantities specified
        for line in self.line_ids:
            if line.stock_quantity < 0:
                raise UserError(_('You must specify the stock quantity for all items before approving the request. Item "%s" has no stock quantity.') % line.item_id.name)

        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.stationery.request', self.id, 'warehouse_approved',
                notes=_('Approved by Warehouse Manager'),
                old_state='warehouse_approval', new_state='hr_approval'
            )

        # Record approval information
        self.warehouse_approval_user_id = self.env.user.id
        self.warehouse_approval_date = fields.Datetime.now()

        # Update state to HR approval
        self.state = 'hr_approval'

        # Find HR manager and notify
        hr_group = self.env.ref('bssic_requests.group_bssic_hr_manager')
        hr_users = hr_group.users
        partner_ids = hr_users.mapped('partner_id.id')
        if partner_ids:
            self.message_subscribe(partner_ids=partner_ids)
            self.message_post(
                body=_('This stationery request has been verified by the warehouse manager and requires your approval.'),
                partner_ids=partner_ids
            )

    def action_approve_hr(self):
        # Check if all items have approved quantities specified
        for line in self.line_ids:
            if line.approved_quantity < 0:
                raise UserError(_('Approved quantity cannot be negative. Item "%s" has a negative approved quantity.') % line.item_id.name)

        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.stationery.request', self.id, 'hr_approved',
                notes=_('Approved by HR Manager'),
                old_state='hr_approval', new_state='pending_receipt'
            )

        # Record approval information
        self.hr_approval_user_id = self.env.user.id
        self.hr_approval_date = fields.Datetime.now()

        # Update state to pending receipt confirmation
        self.state = 'pending_receipt'

        # Notify employee to confirm receipt
        if self.employee_id.user_id:
            self.message_subscribe(partner_ids=[self.employee_id.user_id.partner_id.id])
            self.message_post(
                body=_('Your stationery request has been approved by HR. Please confirm receipt of the items once you receive them.'),
                partner_ids=[self.employee_id.user_id.partner_id.id]
            )

    def action_confirm_receipt_wizard(self):
        """Open the receipt confirmation wizard"""
        return {
            'name': _('Confirm Receipt'),
            'type': 'ir.actions.act_window',
            'res_model': 'bssic.receipt.confirmation.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_stationery_request_id': self.id}
        }

    def action_confirm_receipt(self):
        """Confirm receipt of stationery items"""
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.stationery.request', self.id, 'receipt_confirmed',
                notes=_('Receipt confirmed by employee. Notes: %s') % (self.receipt_notes or _('No notes')),
                old_state='pending_receipt', new_state='completed'
            )

        # Record receipt confirmation information
        self.receipt_confirmation_user_id = self.env.user.id
        self.receipt_confirmation_date = fields.Datetime.now()

        # Update state to completed
        self.state = 'completed'

        # Notify HR and direct manager
        partner_ids = []
        if self.direct_manager_approval_user_id:
            partner_ids.append(self.direct_manager_approval_user_id.partner_id.id)
        if self.hr_approval_user_id:
            partner_ids.append(self.hr_approval_user_id.partner_id.id)

        if partner_ids:
            message_body = _('The employee has confirmed receipt of the stationery items.')
            if self.receipt_notes:
                message_body += _(' Notes: %s') % self.receipt_notes

            self.message_post(
                body=message_body,
                partner_ids=partner_ids
            )

    def action_reject(self):
        return {
            'name': _('Reject Request'),
            'type': 'ir.actions.act_window',
            'res_model': 'bssic.request.reject.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_request_id': self.id, 'default_model': 'bssic.stationery.request'}
        }

    # Note: onchange methods are inherited from stationery_base.py
    # Note: StationeryRequestLine is now in stationery_item.py
