from odoo import models, fields, api, _
from odoo.exceptions import ValidationError


class BSSICPrinterInkItem(models.Model):
    """Printer Ink Item Model"""
    _name = 'bssic.printer.ink.item'
    _description = 'BSSIC Printer Ink Item'
    _order = 'name'

    name = fields.Char(string='Ink Name', required=True, help='Name of the ink cartridge or toner')
    code = fields.Char(string='Ink Code', required=True, help='Manufacturer code for the ink')
    ink_type = fields.Selection([
        ('cartridge', 'Ink Cartridge'),
        ('toner', 'Toner Cartridge'),
        ('ribbon', 'Ribbon'),
        ('other', 'Other')
    ], string='Ink Type', required=True, default='cartridge')
    
    color = fields.Selection([
        ('black', 'Black'),
        ('cyan', 'Cyan'),
        ('magenta', 'Magenta'),
        ('yellow', 'Yellow'),
        ('color', 'Color (Multi)'),
        ('other', 'Other')
    ], string='Color', required=True, default='black')
    
    printer_model = fields.Char(string='Compatible Printer Model', help='Printer models compatible with this ink')
    manufacturer = fields.Char(string='Manufacturer', help='Ink manufacturer (HP, Canon, Epson, etc.)')
    
    # Stock information
    current_stock = fields.Integer(string='Current Stock', default=0, help='Current quantity in stock')
    minimum_stock = fields.Integer(string='Minimum Stock Level', default=1, help='Minimum stock level before reorder')
    
    # Pricing
    unit_cost = fields.Float(string='Unit Cost', help='Cost per unit')
    currency_id = fields.Many2one('res.currency', string='Currency', default=lambda self: self.env.company.currency_id)
    
    # Additional information
    description = fields.Text(string='Description')
    active = fields.Boolean(string='Active', default=True)
    
    # Computed fields
    stock_status = fields.Selection([
        ('in_stock', 'In Stock'),
        ('low_stock', 'Low Stock'),
        ('out_of_stock', 'Out of Stock')
    ], string='Stock Status', compute='_compute_stock_status', store=True)
    
    @api.depends('current_stock', 'minimum_stock')
    def _compute_stock_status(self):
        """Compute stock status based on current and minimum stock"""
        for record in self:
            if record.current_stock <= 0:
                record.stock_status = 'out_of_stock'
            elif record.current_stock <= record.minimum_stock:
                record.stock_status = 'low_stock'
            else:
                record.stock_status = 'in_stock'
    
    @api.constrains('current_stock', 'minimum_stock')
    def _check_stock_values(self):
        """Validate stock values"""
        for record in self:
            if record.current_stock < 0:
                raise ValidationError(_('Current stock cannot be negative.'))
            if record.minimum_stock < 0:
                raise ValidationError(_('Minimum stock level cannot be negative.'))
    
    @api.constrains('code')
    def _check_unique_code(self):
        """Ensure ink code is unique"""
        for record in self:
            if record.code:
                existing = self.search([('code', '=', record.code), ('id', '!=', record.id)])
                if existing:
                    raise ValidationError(_('Ink code "%s" already exists. Please use a unique code.') % record.code)
    
    def name_get(self):
        """Custom name display"""
        result = []
        for record in self:
            name = f"[{record.code}] {record.name}"
            if record.color != 'black':
                name += f" ({record.color.title()})"
            if record.printer_model:
                name += f" - {record.printer_model}"
            result.append((record.id, name))
        return result
    
    @api.model
    def name_search(self, name='', args=None, operator='ilike', limit=100):
        """Enhanced search functionality"""
        if args is None:
            args = []
        
        domain = args[:]
        if name:
            domain += ['|', '|', '|', 
                      ('name', operator, name),
                      ('code', operator, name),
                      ('printer_model', operator, name),
                      ('manufacturer', operator, name)]
        
        return self.search(domain, limit=limit).name_get()
    
    def action_update_stock(self):
        """Action to update stock levels"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Update Stock'),
            'res_model': 'bssic.printer.ink.item',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_current_stock': self.current_stock}
        }


class BSSICPrinterInkRequestLine(models.Model):
    """Printer Ink Request Line Model"""
    _name = 'bssic.printer.ink.request.line'
    _description = 'BSSIC Printer Ink Request Line'
    _order = 'sequence, id'

    sequence = fields.Integer(string='Sequence', default=10)
    request_id = fields.Many2one('bssic.request', string='Request', ondelete='cascade', required=True)
    
    # Ink details
    ink_item_id = fields.Many2one('bssic.printer.ink.item', string='Ink Item', required=True)
    ink_code = fields.Char(string='Ink Code', related='ink_item_id.code', readonly=True, store=True)
    ink_name = fields.Char(string='Ink Name', related='ink_item_id.name', readonly=True, store=True)
    ink_type = fields.Selection(related='ink_item_id.ink_type', readonly=True, store=True)
    color = fields.Selection(related='ink_item_id.color', readonly=True, store=True)
    
    # Request details
    requested_quantity = fields.Integer(string='Requested Quantity', required=True, default=1)
    approved_quantity = fields.Integer(string='Approved Quantity', default=0)
    delivered_quantity = fields.Integer(string='Delivered Quantity', default=0)
    
    # Printer information
    printer_number = fields.Char(string='Printer Number', help='Physical printer number or identifier')
    printer_location = fields.Char(string='Printer Location', help='Department or office location')
    
    # Status
    line_status = fields.Selection([
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('partially_delivered', 'Partially Delivered'),
        ('delivered', 'Delivered'),
        ('rejected', 'Rejected')
    ], string='Status', default='pending', compute='_compute_line_status', store=True)
    
    # Additional information
    notes = fields.Text(string='Notes')
    urgency_reason = fields.Text(string='Urgency Reason', help='Reason if this is an urgent request')
    
    @api.depends('requested_quantity', 'approved_quantity', 'delivered_quantity')
    def _compute_line_status(self):
        """Compute line status based on quantities"""
        for line in self:
            if line.approved_quantity == 0:
                line.line_status = 'pending'
            elif line.delivered_quantity == 0:
                line.line_status = 'approved'
            elif line.delivered_quantity < line.approved_quantity:
                line.line_status = 'partially_delivered'
            else:
                line.line_status = 'delivered'
    
    @api.constrains('requested_quantity', 'approved_quantity', 'delivered_quantity')
    def _check_quantities(self):
        """Validate quantities"""
        for line in self:
            if line.requested_quantity <= 0:
                raise ValidationError(_('Requested quantity must be greater than zero.'))
            if line.approved_quantity < 0:
                raise ValidationError(_('Approved quantity cannot be negative.'))
            if line.delivered_quantity < 0:
                raise ValidationError(_('Delivered quantity cannot be negative.'))
            if line.delivered_quantity > line.approved_quantity:
                raise ValidationError(_('Delivered quantity cannot exceed approved quantity.'))
    
    @api.onchange('ink_item_id')
    def _onchange_ink_item_id(self):
        """Update fields when ink item changes"""
        if self.ink_item_id:
            # Check stock status and warn if low
            if self.ink_item_id.stock_status == 'low_stock':
                return {
                    'warning': {
                        'title': _('Low Stock Warning'),
                        'message': _('The selected ink item "%s" has low stock (Current: %d, Minimum: %d).') % (
                            self.ink_item_id.name, 
                            self.ink_item_id.current_stock, 
                            self.ink_item_id.minimum_stock
                        )
                    }
                }
            elif self.ink_item_id.stock_status == 'out_of_stock':
                return {
                    'warning': {
                        'title': _('Out of Stock Warning'),
                        'message': _('The selected ink item "%s" is currently out of stock.') % self.ink_item_id.name
                    }
                }
