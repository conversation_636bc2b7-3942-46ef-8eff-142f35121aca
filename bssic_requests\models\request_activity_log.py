from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from datetime import datetime
import logging

_logger = logging.getLogger(__name__)


class BSSICRequestActivityLog(models.Model):
    _name = 'bssic.request.activity.log'
    _description = 'BSIC Request Activity Log'
    _order = 'create_date desc'
    _rec_name = 'activity_description'

    # Relations
    request_id = fields.Many2one('bssic.request', string='Request', ondelete='cascade')
    # permission_request_id removed - now using bssic.request for permission requests
    stationery_request_id = fields.Many2one('bssic.stationery.request', string='Stationery Request', ondelete='cascade')

    # Activity Details
    activity_type = fields.Selection([
        ('submitted', 'Submitted by'),
        ('direct_manager_approved', 'Approved by (Manager)'),
        ('audit_manager_approved', 'Approved by (Audit)'),
        ('it_manager_approved', 'Approved by (IT Manager)'),
        ('hr_approved', 'Approved by (HR)'),
        ('warehouse_approved', 'Approved by (Warehouse)'),
        ('assigned', 'Assigned to IT Staff'),
        ('in_progress', 'Started Implementation'),
        ('completed', 'Completed'),
        ('rejected', 'Rejected'),
        ('receipt_confirmed', 'Receipt Confirmed by'),
        ('cancelled', 'Cancelled'),
        ('modified', 'Modified'),
        ('created', 'Request Created'),
        ('field_changed', 'Field Modified'),
        ('attachment_added', 'Attachment Added'),
        ('comment_added', 'Comment Added'),
    ], string='Activity Type', required=True)

    activity_date = fields.Datetime(string='Activity Date', default=fields.Datetime.now, required=True)
    user_id = fields.Many2one('res.users', string='User', required=True, default=lambda self: self.env.user)
    employee_id = fields.Many2one('hr.employee', string='Employee', compute='_compute_employee_id', store=True)

    # Enhanced Activity Details
    activity_description = fields.Text(string='Activity Description', compute='_compute_activity_description', store=True)
    notes = fields.Text(string='Notes')
    old_state = fields.Char(string='Previous State')
    new_state = fields.Char(string='New State')

    # Request Details
    request_name = fields.Char(string='Request Name', compute='_compute_request_details', store=True)
    request_type = fields.Char(string='Request Type', compute='_compute_request_details', store=True)
    request_code = fields.Char(string='Request Code', compute='_compute_request_details', store=True)

    # Field Change Details
    field_name = fields.Char(string='Changed Field')
    old_value = fields.Text(string='Old Value')
    new_value = fields.Text(string='New Value')

    # Additional fields for specific activities
    assigned_to_id = fields.Many2one('hr.employee', string='Assigned To')
    rejection_reason = fields.Text(string='Rejection Reason')

    # System Information
    ip_address = fields.Char(string='IP Address')
    user_agent = fields.Char(string='User Agent')

    @api.depends('user_id')
    def _compute_employee_id(self):
        for record in self:
            employee = self.env['hr.employee'].search([('user_id', '=', record.user_id.id)], limit=1)
            record.employee_id = employee.id if employee else False

    @api.depends('request_id', 'stationery_request_id')
    def _compute_request_details(self):
        """Compute request details for better logging"""
        for record in self:
            if record.request_id:
                record.request_name = record.request_id.name or ''
                record.request_type = record.request_id.request_type_id.name if record.request_id.request_type_id else ''
                record.request_code = record.request_id.request_type_code or ''
            elif record.stationery_request_id:
                record.request_name = record.stationery_request_id.name or ''
                record.request_type = _('Stationery Request')
                record.request_code = 'stationery'
            else:
                record.request_name = ''
                record.request_type = ''
                record.request_code = ''

    @api.depends('activity_type', 'user_id', 'employee_id', 'request_name', 'request_type', 'field_name', 'old_value', 'new_value', 'assigned_to_id', 'old_state', 'new_state')
    def _compute_activity_description(self):
        """Generate detailed activity description"""
        for record in self:
            user_name = record.employee_id.name if record.employee_id else record.user_id.name
            request_info = f"[{record.request_type}] {record.request_name}" if record.request_name else "Request"

            descriptions = {
                'created': _('Request created by %s') % user_name,
                'submitted': _('Request "%s" submitted by %s for approval') % (request_info, user_name),
                'direct_manager_approved': _('Request "%s" approved by Direct Manager (%s)') % (request_info, user_name),
                'audit_manager_approved': _('Request "%s" approved by Audit Manager (%s)') % (request_info, user_name),
                'it_manager_approved': _('Request "%s" approved by IT Manager (%s)') % (request_info, user_name),
                'hr_approved': _('Request "%s" approved by HR (%s)') % (request_info, user_name),
                'warehouse_approved': _('Request "%s" approved by Warehouse (%s)') % (request_info, user_name),
                'assigned': _('Request "%s" assigned to %s by %s') % (request_info, record.assigned_to_id.name if record.assigned_to_id else 'IT Staff', user_name),
                'in_progress': _('Request "%s" implementation started by %s') % (request_info, user_name),
                'completed': _('Request "%s" completed by %s') % (request_info, user_name),
                'rejected': _('Request "%s" rejected by %s') % (request_info, user_name),
                'cancelled': _('Request "%s" cancelled by %s') % (request_info, user_name),
                'modified': _('Request "%s" modified by %s') % (request_info, user_name),
                'field_changed': _('Field "%s" in request "%s" changed by %s from "%s" to "%s"') % (
                    record.field_name or 'Unknown', request_info, user_name,
                    record.old_value or 'Empty', record.new_value or 'Empty'
                ),
                'attachment_added': _('Attachment added to request "%s" by %s') % (request_info, user_name),
                'comment_added': _('Comment added to request "%s" by %s') % (request_info, user_name),
                'receipt_confirmed': _('Receipt confirmed for request "%s" by %s') % (request_info, user_name),
            }

            base_description = descriptions.get(record.activity_type, f'{record.activity_type} by {user_name}')

            # Add state change information if available
            if record.old_state and record.new_state and record.old_state != record.new_state:
                base_description += f' (State: {record.old_state} → {record.new_state})'

            record.activity_description = base_description

    @api.constrains('request_id', 'stationery_request_id')
    def _check_request_reference(self):
        for record in self:
            if not any([record.request_id, record.stationery_request_id]):
                raise ValidationError(_('At least one request reference must be set.'))

    def name_get(self):
        result = []
        for record in self:
            activity_name = dict(record._fields['activity_type'].selection).get(record.activity_type, record.activity_type)
            name = f"{activity_name} - {record.activity_date.strftime('%Y-%m-%d %H:%M')}"
            result.append((record.id, name))
        return result

    def _get_client_info(self):
        """Get client information for logging"""
        try:
            request = self.env.context.get('request')
            if request:
                return {
                    'ip_address': request.httprequest.environ.get('REMOTE_ADDR'),
                    'user_agent': request.httprequest.environ.get('HTTP_USER_AGENT', '')[:255]  # Limit length
                }
        except:
            pass
        return {'ip_address': None, 'user_agent': None}

    @api.model
    def create_activity_log(self, request_model, request_id, activity_type, notes=None, old_state=None, new_state=None,
                           assigned_to_id=None, rejection_reason=None, field_name=None, old_value=None, new_value=None):
        """
        Create an enhanced activity log entry with detailed information

        :param request_model: Model name ('bssic.request', 'bssic.stationery.request', etc.)
        :param request_id: ID of the request
        :param activity_type: Type of activity
        :param notes: Additional notes
        :param old_state: Previous state
        :param new_state: New state
        :param assigned_to_id: Employee assigned to (if applicable)
        :param rejection_reason: Reason for rejection (if applicable)
        :param field_name: Name of changed field (for field_changed activity)
        :param old_value: Previous value of changed field
        :param new_value: New value of changed field
        """
        if not request_id:
            _logger.warning("Attempted to create activity log without request_id")
            return False

        try:
            # Get client information
            client_info = self._get_client_info()

            vals = {
                'activity_type': activity_type,
                'notes': notes,
                'old_state': old_state,
                'new_state': new_state,
                'assigned_to_id': assigned_to_id,
                'rejection_reason': rejection_reason,
                'field_name': field_name,
                'old_value': str(old_value) if old_value is not None else None,
                'new_value': str(new_value) if new_value is not None else None,
                'ip_address': client_info['ip_address'],
                'user_agent': client_info['user_agent'],
            }

            # Set the appropriate request field based on model
            if request_model in ['bssic.request', 'bssic.permission.request']:
                # Both bssic.request and legacy bssic.permission.request now use bssic.request model
                vals['request_id'] = request_id
            elif request_model == 'bssic.stationery.request':
                vals['stationery_request_id'] = request_id
            else:
                # If model is not recognized, don't create the log
                _logger.warning(f"Unrecognized request model: {request_model}")
                return False

            log_entry = self.create(vals)

            # Log the activity creation for debugging
            _logger.info(f"Activity log created: {activity_type} for {request_model} ID {request_id} by user {self.env.user.name}")

            return log_entry

        except Exception as e:
            _logger.error(f"Failed to create activity log: {str(e)}")
            return False

    def get_activity_display_name(self):
        """Get a user-friendly display name for the activity"""
        activity_names = {
            'submitted': _('Submitted by'),
            'direct_manager_approved': _('Approved by (Manager)'),
            'audit_manager_approved': _('Approved by (Audit)'),
            'it_manager_approved': _('Approved by (IT Manager)'),
            'hr_approved': _('Approved by (HR)'),
            'warehouse_approved': _('Approved by (Warehouse)'),
            'assigned': _('Assigned to IT Staff'),
            'in_progress': _('Started Implementation'),
            'completed': _('Completed'),
            'rejected': _('Rejected'),
            'receipt_confirmed': _('Receipt Confirmed by'),
            'cancelled': _('Cancelled'),
            'modified': _('Modified'),
        }
        return activity_names.get(self.activity_type, self.activity_type)

    def get_state_display_name(self, state):
        """Get a user-friendly display name for states"""
        state_names = {
            'draft': _('Draft'),
            'submitted': _('Submitted'),
            'direct_manager': _('Direct Manager Approval'),
            'direct_manager_approval': _('Direct Manager Approval'),
            'audit_manager': _('Audit Manager Approval'),
            'dept_manager_approval': _('Department Manager Approval'),
            'it_manager': _('IT Manager Approval'),
            'it_manager_approval': _('IT Manager Approval'),
            'hr_approval': _('HR Approval'),
            'warehouse_approval': _('Warehouse Approval'),
            'assigned': _('Assigned to IT Staff'),
            'in_progress': _('In Progress'),
            'completed': _('Completed'),
            'rejected': _('Rejected'),
            'cancelled': _('Cancelled'),
        }
        return state_names.get(state, state)
