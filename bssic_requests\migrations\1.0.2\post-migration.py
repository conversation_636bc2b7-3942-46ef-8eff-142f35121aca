# -*- coding: utf-8 -*-
"""
Migration script to handle bssic.permission.request model transition
"""

def migrate(cr, version):
    """
    Handle the transition from bssic.permission.request to bssic.request
    """

    print("🔄 Starting migration to handle bssic.permission.request transition...")

    # Clean up model registry references
    try:
        # Remove old model from ir_model if it exists
        cr.execute("""
            DELETE FROM ir_model
            WHERE model = 'bssic.permission.request'
        """)

        # Update ir_model_data references to point to bssic.request
        cr.execute("""
            UPDATE ir_model_data
            SET model = 'bssic.request'
            WHERE model = 'bssic.permission.request'
        """)

        # Remove any access rules for the old model
        cr.execute("""
            DELETE FROM ir_model_access
            WHERE model_id IN (
                SELECT id FROM ir_model WHERE model = 'bssic.permission.request'
            )
        """)

        # Remove any record rules for the old model
        cr.execute("""
            DELETE FROM ir_rule
            WHERE model_id IN (
                SELECT id FROM ir_model WHERE model = 'bssic.permission.request'
            )
        """)

        print("✅ Model registry cleanup completed")

    except Exception as e:
        print(f"⚠️  Model cleanup warning: {e}")

    print("✅ Migration completed successfully - bssic.permission.request now redirects to bssic.request")
