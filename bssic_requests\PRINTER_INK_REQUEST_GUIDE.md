# دليل طلب حبر الطابعة - Printer Ink Request Guide

## نظرة عامة / Overview

تم إضافة نوع طلب جديد خاص بطلب حبر الطابعة (Printer Ink Request) إلى مديول bssic_requests. هذا النوع من الطلبات يسمح للموظفين بطلب خراطيش الحبر والتونر للطابعات مع إدارة شاملة للمخزون.

## الميزات الجديدة / New Features

### 1. إدارة عناصر الحبر (Ink Items Management)
- **قاعدة بيانات شاملة** لجميع أنواع الحبر والتونر
- **تتبع المخزون** مع مستويات التنبيه
- **معلومات تفصيلية** عن كل عنصر حبر
- **دعم متعدد الشركات** (<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Brother, etc.)

### 2. أنواع الحبر المدعومة
```python
ink_types = [
    ('cartridge', 'Ink Cartridge'),    # خراطيش الحبر
    ('toner', 'Toner Cartridge'),      # خراطيش التونر
    ('ribbon', 'Ribbon'),              # أشرطة الطباعة
    ('other', 'Other')                 # أنواع أخرى
]

colors = [
    ('black', 'Black'),                # أسود
    ('cyan', 'Cyan'),                  # سماوي
    ('magenta', 'Magenta'),            # بنفسجي
    ('yellow', 'Yellow'),              # أصفر
    ('color', 'Color (Multi)'),        # متعدد الألوان
    ('other', 'Other')                 # ألوان أخرى
]
```

### 3. نظام طلب متقدم
- **طلب متعدد العناصر** في طلب واحد
- **تفاصيل الطابعة** (رقم الطابعة، الموقع)
- **مستويات الأولوية** (عادي، عاجل، حرج)
- **ملاحظات تفصيلية** لكل عنصر

## هيكل البيانات / Data Structure

### 1. نموذج عنصر الحبر (Printer Ink Item)
```python
class BSSICPrinterInkItem(models.Model):
    _name = 'bssic.printer.ink.item'
    
    # معلومات أساسية
    name = fields.Char('Ink Name')                    # اسم الحبر
    code = fields.Char('Ink Code')                    # رمز الحبر
    ink_type = fields.Selection(...)                  # نوع الحبر
    color = fields.Selection(...)                     # اللون
    manufacturer = fields.Char('Manufacturer')        # الشركة المصنعة
    printer_model = fields.Char('Compatible Printer') # الطابعات المتوافقة
    
    # إدارة المخزون
    current_stock = fields.Integer('Current Stock')   # المخزون الحالي
    minimum_stock = fields.Integer('Minimum Stock')   # الحد الأدنى
    stock_status = fields.Selection(...)              # حالة المخزون
    
    # معلومات التكلفة
    unit_cost = fields.Float('Unit Cost')             # التكلفة لكل وحدة
    currency_id = fields.Many2one('res.currency')     # العملة
```

### 2. نموذج سطر طلب الحبر (Ink Request Line)
```python
class BSSICPrinterInkRequestLine(models.Model):
    _name = 'bssic.printer.ink.request.line'
    
    # معلومات الحبر
    ink_item_id = fields.Many2one('bssic.printer.ink.item')
    requested_quantity = fields.Integer('Requested Quantity')
    approved_quantity = fields.Integer('Approved Quantity')
    delivered_quantity = fields.Integer('Delivered Quantity')
    
    # معلومات الطابعة
    printer_number = fields.Char('Printer Number')
    printer_location = fields.Char('Printer Location')
    
    # حالة السطر
    line_status = fields.Selection([
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('partially_delivered', 'Partially Delivered'),
        ('delivered', 'Delivered'),
        ('rejected', 'Rejected')
    ])
```

## كيفية الاستخدام / How to Use

### 1. إنشاء طلب حبر جديد
1. انتقل إلى **BSIC Requests > Requests > Printer Ink Request**
2. اضغط على **Create** لإنشاء طلب جديد
3. املأ المعلومات الأساسية:
   - **Employee**: الموظف الطالب
   - **Urgency Level**: مستوى الأولوية
   - **Delivery Location**: مكان التسليم
   - **Additional Notes**: ملاحظات إضافية

### 2. إضافة عناصر الحبر
1. في قسم **Requested Ink Items**، اضغط **Add a line**
2. اختر **Ink Item** من القائمة المنسدلة
3. حدد **Requested Quantity** (الكمية المطلوبة)
4. أدخل **Printer Number** و **Printer Location**
5. أضف **Notes** إذا لزم الأمر

### 3. تقديم الطلب
1. راجع جميع المعلومات
2. اضغط **Submit** لتقديم الطلب للموافقة
3. سيتم إرسال الطلب لسير الموافقة المعتاد

## سير الموافقة / Approval Workflow

### 1. المراحل الأساسية
```
Draft → Direct Manager → Audit Manager → IT Manager → Assigned → In Progress → Completed
```

### 2. الأدوار والصلاحيات
- **Employee**: إنشاء وتعديل الطلبات في مرحلة Draft
- **Direct Manager**: موافقة المدير المباشر
- **Audit Manager**: موافقة مدير التدقيق
- **IT Manager**: موافقة مدير تقنية المعلومات وتعيين المسؤول
- **IT Staff**: تنفيذ الطلب وتحديث الكميات المسلمة

### 3. إدارة المخزون
- **تحقق تلقائي** من توفر المخزون عند الطلب
- **تنبيهات** عند انخفاض المخزون
- **تتبع الكميات** المطلوبة والموافق عليها والمسلمة

## البيانات الأساسية المتوفرة / Available Master Data

### 1. خراطيش HP
- HP 304 Black/Color (DeskJet 2620, 2630, 3720, 3730)
- HP 678 Black/Color (DeskJet 1515, 2515, 2545, 2645)
- HP CF217A Toner (LaserJet Pro M102, M130)

### 2. خراطيش Canon
- Canon PG-540 Black (PIXMA MG2150, MG3150, MG4150)
- Canon CL-541 Color (PIXMA MG2150, MG3150, MG4150)
- Canon CRG-725 Toner (LBP6000, LBP6020)

### 3. خراطيش Epson
- Epson T664 Black/Cyan/Magenta/Yellow (L120, L220, L360, L365)

### 4. خراطيش Brother
- Brother LC3213 Black/Cyan/Magenta/Yellow (DCP-J572DW, MFC-J491DW)

### 5. خراطيش متوافقة
- Compatible HP 304 Black/Color (بديل اقتصادي)

## إدارة المخزون / Inventory Management

### 1. حالات المخزون
```python
stock_status = [
    ('in_stock', 'In Stock'),      # متوفر
    ('low_stock', 'Low Stock'),    # مخزون منخفض
    ('out_of_stock', 'Out of Stock') # نفد المخزون
]
```

### 2. التنبيهات التلقائية
- **تنبيه عند الطلب**: إذا كان المخزون منخفض أو نافد
- **تحديث تلقائي**: لحالة المخزون بناءً على الكميات
- **تقارير دورية**: عن استهلاك الحبر

### 3. إدارة التكاليف
- **تتبع التكلفة** لكل وحدة حبر
- **حساب التكلفة الإجمالية** للطلبات
- **مقارنة الأسعار** بين الأصلي والمتوافق

## التقارير والإحصائيات / Reports & Statistics

### 1. تقارير الاستهلاك
```python
def get_ink_usage_statistics(date_from, date_to):
    return {
        'total_requests': 50,                    # إجمالي الطلبات
        'completed_requests': 45,               # الطلبات المكتملة
        'pending_requests': 3,                  # الطلبات المعلقة
        'in_progress_requests': 2,              # الطلبات قيد التنفيذ
        'total_items_requested': 150,           # إجمالي العناصر المطلوبة
        'most_requested_items': {...},          # أكثر العناصر طلباً
        'department_usage': {...}               # الاستهلاك حسب القسم
    }
```

### 2. تقارير المخزون
- **مستويات المخزون الحالية**
- **العناصر التي تحتاج إعادة طلب**
- **تاريخ آخر تحديث للمخزون**

### 3. تقارير التكلفة
- **التكلفة الشهرية للحبر**
- **مقارنة التكاليف بين الأقسام**
- **توفير التكلفة باستخدام البدائل المتوافقة**

## الميزات المتقدمة / Advanced Features

### 1. فحص توفر المخزون
```python
def action_check_stock_availability(self):
    """فحص توفر المخزون لجميع العناصر المطلوبة"""
    unavailable_items = []
    low_stock_items = []
    
    for line in self.printer_ink_line_ids:
        if line.ink_item_id.stock_status == 'out_of_stock':
            unavailable_items.append(line.ink_item_id.name)
        elif line.ink_item_id.stock_status == 'low_stock':
            low_stock_items.append(line.ink_item_id.name)
```

### 2. إنشاء مذكرة تسليم
```python
def action_generate_delivery_note(self):
    """إنشاء مذكرة تسليم للعناصر الموافق عليها"""
    approved_lines = self.printer_ink_line_ids.filtered(
        lambda l: l.approved_quantity > 0
    )
    # إنشاء تقرير PDF للتسليم
```

### 3. البحث المتقدم
- **البحث بالاسم أو الرمز**
- **فلترة حسب نوع الحبر**
- **فلترة حسب الشركة المصنعة**
- **فلترة حسب حالة المخزون**

## أمثلة عملية / Practical Examples

### مثال 1: طلب حبر لطابعة HP
```
Employee: أحمد محمد
Department: المحاسبة
Request Type: Printer Ink Request
Urgency: Normal
Delivery Location: قسم المحاسبة - الطابق الثاني

Items:
1. HP 304 Black Ink Cartridge
   - Quantity: 2
   - Printer Number: HP-ACC-001
   - Printer Location: مكتب المحاسب الرئيسي

2. HP 304 Color Ink Cartridge
   - Quantity: 1
   - Printer Number: HP-ACC-001
   - Printer Location: مكتب المحاسب الرئيسي
```

### مثال 2: طلب عاجل لتونر ليزر
```
Employee: سارة أحمد
Department: الموارد البشرية
Request Type: Printer Ink Request
Urgency: Urgent
Delivery Location: قسم الموارد البشرية

Items:
1. HP CF217A Black Toner Cartridge
   - Quantity: 1
   - Printer Number: HP-HR-LASER-01
   - Printer Location: مكتب مدير الموارد البشرية
   - Urgency Reason: الطابعة نفد منها التونر ونحتاج طباعة عقود عمل عاجلة
```

## الفوائد / Benefits

### 1. تنظيم أفضل
- **مركزية طلبات الحبر** في نظام واحد
- **تتبع شامل** لجميع الطلبات
- **سجل كامل** للاستهلاك

### 2. توفير التكاليف
- **مراقبة الاستهلاك** ومنع الهدر
- **مقارنة الأسعار** بين البدائل
- **تخطيط أفضل** للمشتريات

### 3. كفاءة العمل
- **تقليل وقت المعالجة** للطلبات
- **تنبيهات تلقائية** للمخزون المنخفض
- **تقارير دورية** للإدارة

### 4. الشفافية
- **تتبع كامل** لدورة حياة الطلب
- **سجل مفصل** لجميع الأنشطة
- **مساءلة واضحة** لكل مرحلة

هذا النظام الجديد يوفر إدارة شاملة ومتقدمة لطلبات حبر الطابعة مع تتبع دقيق للمخزون والتكاليف! 🖨️✨
