from odoo import models, fields, api

class BSSICRequestType(models.Model):
    _name = 'bssic.request.type'
    _description = 'BSSIC Request Type'

    name = fields.Char('Name', required=True)
    code = fields.Char('Code')
    active = fields.<PERSON><PERSON>an('Active', default=True)
    description = fields.Text('Description')
    show_password_fields = fields.<PERSON><PERSON><PERSON>('Show Password Fields')
    show_usb_fields = fields.<PERSON><PERSON><PERSON>('Show USB Fields')
    show_extension_fields = fields.<PERSON><PERSON>an('Show Extension Fields')
    show_permission_fields = fields.<PERSON><PERSON><PERSON>('Show Permission Fields')
    show_email_fields = fields.<PERSON><PERSON><PERSON>('Show Email Fields')
    show_authorization_delegation_fields = fields.<PERSON><PERSON><PERSON>('Show Authorization Delegation Fields')
    show_free_entry_fields = fields.<PERSON><PERSON><PERSON>('Show Free Entry Fields')
    show_printer_ink_fields = fields.<PERSON><PERSON><PERSON>('Show Printer Ink Fields')